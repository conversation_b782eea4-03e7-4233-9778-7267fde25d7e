<template>
  <div class="app-container">
    <el-container :style="{ height: '100%' }">
      <el-main>
        <el-collapse v-model="collapse">
          <el-collapse-item title="添加到货计划" :name="1">
            <el-form
              ref="basic"
              label-position="right"
              :model="basicFormModel"
              :rules="basicFormRules"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="purchaseOrderCode" label="关联采购单号：">
                    <el-input
                      v-model="basicFormModel.purchaseOrderCode"
                      placeholder="请输入物料编码"
                      max-length="20"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="materialCode" label="物料选择：">
                    <el-select
                      v-model="basicFormModel.materialCode"
                      placeholder="请选择"
                      style="width: 100%"
                      @change="handleMaterialChange"
                    >
                      <el-option
                        v-for="(item, index) in materialOptions"
                        :key="index"
                        :label="item.materialName"
                        :value="item.materialCode"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item
                    prop="quantity"
                    label="计划数量："
                    class="el-form-item-width"
                  >
                    <el-input
                      v-model="basicFormModel.quantity"
                      type="number"
                      placeholder="请输入计划数量"
                    />
                    <span style="position: absolute; margin-left: 10px">{{
                      unitName
                    }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="planDeliveryTime" label="计划交货时间：">
                    <el-date-picker
                      v-model="basicFormModel.planDeliveryTime"
                      type="date"
                      style="width: 100%"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="supplierEntCode" label="供应商选择：">
                    <el-select
                      v-model="basicFormModel.supplierEntCode"
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="(item, index) in supplierOptions"
                        :key="index"
                        :label="item.companyName"
                        :value="item.entCode"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="receiveAddress" label="收货地址：">
                    <el-input
                      v-model="basicFormModel.receiveAddress"
                      placeholder="请输入收货地址"
                      max-length="20"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="remark" label="备注：">
                    <el-switch
                      v-model="isRemark"
                      active-text="备注给对方"
                      inactive-text=""
                    />
                    <el-input
                      v-model="basicFormModel.remark"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入备注"
                      max-length="200"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>

            <el-footer class="button-container">
              <el-button @click="onCancel">取消</el-button>
              <el-button type="primary" @click="onSave">保存</el-button>
            </el-footer>
          </el-collapse-item>
        </el-collapse>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import accApi from "@/api/acc/acc";
export default {
  name: "ArrivalPlanAdd",
  data() {
    return {
      materialOptions: [],
      supplierOptions: [],
      unitName: "",
      collapse: [1],
      isRemark: true,
      basicFormModel: {
        purchaseOrderCode: "",
        quantity: "",
        remark: "",
        materialCode: "",
        supplierEntCode: undefined,
        receiveAddress: "",
        planDeliveryTime: "",
      },
      basicFormRules: {
        // medicinalProductName: [
        //   { required: true, message: "请输入物料名称", trigger: "blur" },
        // ],
        materialCode: [
          { required: true, message: "请选择物料类型", trigger: "change" },
        ],
        quantity: [
          { required: true, message: "请输入计划数量", trigger: "blur" },
        ],
        statisticUnits: [
          { required: true, message: "请选择单位", trigger: "change" },
        ],
        planDeliveryTime: [
          { required: true, message: "请选择计划交货时间", trigger: "change" },
        ],
        supplierEntCode: [
          { required: true, message: "请选择供应商", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.selectMaterial();
    this.selectSupplier();
  },
  methods: {
    handleMaterialChange() {
      this.unitName = this.materialOptions.find(
        (item) => item.materialCode === this.basicFormModel.materialCode
      ).unitName;
    },
    selectMaterial() {
      accApi.selectMaterial().then((res) => {
        this.materialOptions = res.data;
      });
    },
    selectSupplier() {
      accApi.selectSupplier().then((res) => {
        this.supplierOptions = res.data;
      });
    },
    onCancel() {
      this.$router.replace({ path: "arrivalPlan" });
    },
    onSave() {
      this.$refs.basic.validate((valid) => {
        if (valid) {
          // const api = this.action === "add" ? "addDeliveryPlan" : "";
          const tem = {
            ...this.basicFormModel,
            remarkSwitch: this.isRemark ? 1 : 0,
          };
          accApi.addDeliveryPlan(tem).then((res) => {
            this.$message.success("保存成功");
            this.onCancel();
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.button-container {
  line-height: 60px;
  text-align: center;
}
</style>
