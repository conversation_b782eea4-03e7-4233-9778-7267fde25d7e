<template>
  <div class="app-container">
    <!--    &lt;!&ndash; 搜索框 &ndash;&gt;-->
    <data-select
      :search-data.sync="search"
      :button-data="buttonData"
      @return-search="
        (data) => {
          searchHelper.search(data);
        }
      "
      @return-reset="searchHelper.reset"
    >
    </data-select>
    <data-table
      ref="dataTable"
      :table-data="searchHelper.dataList"
      :column="column"
      :pagination.sync="searchHelper.pagination"
      @search-event="
        () => {
          searchHelper.handleQuery();
        }
      "
    >
      <template v-slot:statusName="{ row }">
        <el-tag v-if="row.statusName == '协同中'" type="primary">{{
          row.statusName
        }}</el-tag>
        <el-tag v-if="row.statusName == '待对方确认'" type="success">{{
          row.statusName
        }}</el-tag>
        <el-tag v-if="row.statusName == '待我方审核'" type="warning">{{
          row.statusName
        }}</el-tag>
        <el-tag v-if="row.statusName == '已冻结'" type="danger">{{
          row.statusName
        }}</el-tag>
        <el-tag v-if="row.statusName == '未达成协同'" type="info">{{
          row.statusName
        }}</el-tag>
      </template>
    </data-table>
    <simple-data-dialog
      class="dialog-style"
      :title="
        dialogMode === 'confirm'
          ? '供应商审核'
          : dialogMode === 'edit'
          ? '编辑供应商信息'
          : '邀请供应商'
      "
      :visible="aoTuMationDialogVisible"
      size="small"
      :show-close="true"
      :before-close="handleBeforeClose"
      :check-dirty="true"
    >
      <el-form label-width="80px" v-if="dialogMode === 'add'">
        <el-form-item
          prop="identificationRuleId"
          label="企业选择："
          style="width: 90%"
        >
          <el-select
            v-model="companyName"
            filterable
            remote
            reserve-keyword
            placeholder="企业前缀/企业名称/统一社会信用代码"
            :remote-method="remoteSearchCompany"
            :loading="searchLoading"
            @change="changeCompany($event)"
          >
            <el-option
              v-for="(item, index) in options"
              :key="index"
              :label="item.companyName"
              :value="item.entCode"
            >
              <div>
                <div>{{ item.companyName }}</div>
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    color: #8492a6;
                    font-size: 13px;
                  "
                >
                  <span>{{ item.entPrefix }}</span>
                  <span>{{ item.entCode }}</span>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-divider v-if="dialogMode === 'add'"></el-divider>
      <div class="info-form">
        <el-form label-width="120px">
          <el-form-item
            v-for="item in formItems"
            :key="item.prop"
            :label="item.label"
            class="right-label"
          >
            <template v-if="item.prop === 'businessLicense'">
              <magnify-img
                v-if="form[item.prop]"
                :src="form[item.prop]"
                :width="300"
                :height="300"
                :zoom="2"
              />
            </template>
            <template v-else-if="item.prop === 'legalPersonName'">
              <div v-if="!!form[item.prop]" style="width: 300px">
                <span style="margin-right: 10px">{{ form[item.prop] }}</span>
                <el-tag size="mini">已实名核验</el-tag>
              </div>
            </template>
            <template
              v-else-if="
                item.prop === 'contactPersonName' ||
                item.prop === 'contactPersonPhone' ||
                item.prop === 'customerCode'
              "
            >
              <span
                v-if="
                  !!companyName ||
                  dialogMode === 'edit' ||
                  dialogMode === 'confirm'
                "
              >
                <el-input v-model="form[item.prop]" style="width: 300px" />
              </span>
            </template>
            <span v-else class="info-value">{{ form[item.prop] || "" }}</span>
          </el-form-item>
        </el-form>
      </div>

      <el-footer class="button-container">
        <!--        <el-button @click="closeDialog">取消</el-button>-->
        <el-button
          v-if="dialogMode === 'add'"
          type="primary"
          @click="submitAoToMation"
          >发出邀请</el-button
        >
        <el-button
          v-if="dialogMode === 'confirm'"
          type="danger"
          @click="handleReject"
          >拒绝</el-button
        >
        <el-button
          v-if="dialogMode === 'confirm'"
          type="success"
          @click="handleConfirm"
          >通过审核</el-button
        >
        <el-button
          v-if="dialogMode === 'edit'"
          type="primary"
          @click="handleSubmitEdit"
          >保存</el-button
        >
      </el-footer>
    </simple-data-dialog>

    <simple-data-dialog
      title="详情"
      :visible="showDetail"
      size="small"
      :show-close="true"
      class="dialog-style"
      :check-dirty="true"
      :before-close="handleDetailClose"
    >
      <div class="info-form">
        <el-form label-width="120px">
          <el-form-item
            v-for="item in formItems"
            :key="item.prop"
            :label="item.label"
            class="right-label"
          >
            <template v-if="item.prop === 'businessLicense'">
              <magnify-img
                v-if="form[item.prop]"
                :src="form[item.prop]"
                :width="300"
                :height="300"
                :zoom="2"
              />
            </template>
            <template v-else-if="item.prop === 'legalPersonName'">
              <div v-if="!!form[item.prop]" style="width: 300px">
                <span style="margin-right: 10px">{{ form[item.prop] }}</span>
                <el-tag size="mini">已实名核验</el-tag>
              </div>
            </template>
            <span v-else class="info-value">{{ form[item.prop] || "" }}</span>
          </el-form-item>
        </el-form>
      </div>

      <el-footer class="button-container"> </el-footer>
    </simple-data-dialog>
  </div>
</template>

<script>
import DataSelect from "@/components/DataSelect/index.vue";
import DataTable from "@/components/DataTable/index.vue";
import SimpleDataDialog from "@/components/SimpleDataDialog/index.vue";
import MagnifyImg from "@/components/MagnifyImg/index.vue"; // 引入 magnifyImg 组件
import accApi from "@/api/acc/acc";

export default {
  name: "MaterialList",
  components: { SimpleDataDialog, DataTable, DataSelect, MagnifyImg }, // 注册 magnifyImg 组件
  data() {
    return {
      statusMap: ["协同中", "待对方接收", "待我方审核", "已冻结", "未达成协同"],
      // 搜索组件数据
      search: {
        searchKey: {
          label: "客户编码/客户名称/企业前缀",
          value: null,
          type: "input",
          option: {
            placeholder: "请输入客户编码/客户名称/企业前缀",
          },
        },
        status: {
          label: "状态",
          value: null,
          type: "select",
          option: {
            clearable: false,
            selectOptions: [
              { value: "协同中", label: "协同中" },
              { value: "待对方确认", label: "待对方确认" },
              { value: "待我方审核", label: "待我方审核" },
              { value: "已冻结", label: "已冻结" },
              { value: "未达成协同", label: "未达成协同" },
            ],
            placeholder: "请选择同步状态",
          },
        },
      },
      buttonData: [
        {
          label: "添加",
          action: this.onAddClick,
          permission: "all",
        },
      ],
      searchHelper: new this.$searchHelper({ api: accApi.querySupplierList }),
      // 表格数据data
      tempData: {
        pageNum: 1,
        pageSize: 10,
        pages: 2,
        prePage: 0,
        list: [],
      },
      // 表格列,按钮数据
      column: {
        // 表头
        data: [
          {
            label: "序号",
            prop: "index",
            sortable: false,
          },
          {
            label: "企业前缀",
            prop: "supplierEntPrefix",
            sortable: false,
          },
          {
            label: "协同状态",
            prop: "statusName",
            sortable: false,
            slotName: "statusName",
          },
          {
            label: "客户名称",
            prop: "companyName",
            sortable: false,
          },
          {
            label: "统一社会信用代码",
            prop: "supplierEntCode",
            sortable: false,
          },
          {
            label: "客户编码",
            prop: "supplierCode",
            sortable: false,
          },
          {
            label: "地址",
            prop: "companyAddress",
            sortable: false,
          },
          {
            label: "联系人",
            prop: "supplierContactPerson",
            sortable: false,
          },
          {
            label: "联系电话",
            prop: "supplierContactPhone",
            sortable: false,
          },
        ],
        operation: {
          label: "操作",
          width: "220px",
          data: (row) => {
            let button = [];
            switch (row.statusName) {
              case "协同中":
                button = [
                  {
                    label: "详情",
                    action: this.onDetail,
                    permission: "all",
                  },
                  {
                    label: "编辑",
                    action: this.onEditClick,
                    permission: "all",
                  },
                  {
                    label: "冻结",
                    action: this.onFreeze,
                    permission: "all",
                  },
                ];
                break;
              case "待对方确认":
                button = [
                  {
                    label: "详情",
                    action: this.onDetail,
                    permission: "all",
                  },
                  {
                    label: "撤销",
                    action: this.onCancel,
                    permission: "all",
                  },
                ];
                break;
              case "待我方审核":
                button = [
                  {
                    label: "审核",
                    action: this.handleShenHE,
                    permission: "all",
                  },
                ];
                break;
              case "已冻结":
                button = [
                  {
                    label: "详情",
                    action: this.onDetail,
                    permission: "all",
                  },
                  {
                    label: "解冻",
                    action: this.onUnFreeze,
                    permission: "all",
                  },
                ];
                break;
              case "未达成协同":
                button = [
                  {
                    label: "删除",
                    action: this.onDeleteClick,
                    permission: "all",
                  },
                ];
                break;
            }
            return button;
          },
        },
      },
      aoTuMationDialogVisible: false,
      dialogMode: "add",
      showDetail: false,
      templateList: [],
      ruleList: [],
      options: [],
      companyName: "",
      searchLoading: false,
      form: {
        entPrefix: "",
        companyName: "",
        entCode: "",
        detailedAddress: "",
        businessLicense: "",
        legalPersonName: "",
        contactPersonName: "",
        contactPersonPhone: "",
        customerCode: "",
      },
      formItems: [
        { prop: "entPrefix", label: "企业前缀:" },
        { prop: "companyName", label: "客户名称:" },
        { prop: "entCode", label: "统一社会信用代码:" },
        { prop: "detailedAddress", label: "地址:" },
        { prop: "businessLicense", label: "营业执照:" },
        { prop: "legalPersonName", label: "法定代表人:" },
        { prop: "contactPersonName", label: "联系人:" },
        { prop: "contactPersonPhone", label: "联系电话:" },
        { prop: "customerCode", label: "客户编码:" },
      ],
      labelPosition: "right",
      companyData: [],
      showMagnifier: false,
      magnifierStyle: {},
      magnifierImageStyle: {},
      showSelection: false,
      selectionStyle: {},
      originalFormInDialog: null,
    };
  },
  mounted() {
    this.searchHelper.handleQuery();
  },
  methods: {
    // 远程搜索企业
    async remoteSearchCompany(query) {
      if (query !== "") {
        this.searchLoading = true;
        try {
          const res = await accApi.queryCompanyInfo({
            companyName: query,
          });
          this.options = res.data || [];
        } catch (error) {
          console.error("搜索企业失败:", error);
          this.options = [];
        } finally {
          this.searchLoading = false;
        }
      } else {
        this.options = [];
      }
    },
    onFreeze(row) {
      this.$confirm("是否确定冻结？", "提示", { type: "warning" }).then(() => {
        accApi.updateCustomer({ id: row.id, status: 3 }).then((res) => {
          this.$message({
            message: "冻结成功",
            type: "success",
          });
          this.searchHelper.handleQuery();
        });
      });
    },
    onUnFreeze(row) {
      this.$confirm("是否确定解冻？", "提示", { type: "warning" }).then(() => {
        accApi.updateCustomer({ id: row.id, status: 5 }).then((res) => {
          this.$message({
            message: "解冻成功",
            type: "success",
          });
          this.searchHelper.handleQuery();
        });
      });
    },
    // 撤销
    onCancel(row) {
      this.$confirm("是否确定撤销？", "提示", { type: "warning" }).then(() => {
        accApi.delSupplier({ id: row.id }).then((res) => {
          this.$message({
            message: "撤销成功",
            type: "success",
          });
          this.searchHelper.handleQuery();
        });
      });
    },
    // 详情
    onDetail(row) {
      accApi.querySupplierDetail({ id: row.id }).then((res) => {
        this.form.entPrefix = res.data.supplierEntPrefix;
        this.form.companyName = res.data.supplierEntName;
        this.form.entCode = res.data.supplierEntCode;
        this.form.detailedAddress = res.data.address;
        this.form.businessLicense = res.data.businessLicense;
        this.form.legalPersonName = res.data.legalPersonName;
        this.form.contactPersonName = res.data.supplierContactPerson;
        this.form.contactPersonPhone = res.data.supplierContactPhone;
        this.form.customerCode = res.data.supplierCode;
        this.showDetail = true;
      });
    },
    // 发出邀请
    submitAoToMation() {
      if (this.companyName) {
        accApi
          .addSupplier({
            supplierCode: this.form.customerCode,
            supplierEntCode: this.form.entCode,
            supplierEntPrefix: this.form.entPrefix,
            supplierContactPerson: this.form.contactPersonName,
            supplierContactPhone: this.form.contactPersonPhone,
          })
          .then((res) => {
            this.$message({
              message: "邀请成功",
              type: "success",
            });
            this.aoTuMationDialogVisible = false;
            this.searchHelper.handleQuery();
          });
      } else {
        this.$message.error("请先选择企业");
      }
    },
    handleShenHE(row) {
      accApi.querySupplierDetail({ id: row.id }).then((res) => {
        this.form.entPrefix = res.data.supplierEntPrefix;
        this.form.companyName = res.data.supplierEntName;
        this.form.entCode = res.data.supplierEntCode;
        this.form.detailedAddress = res.data.address;
        this.form.businessLicense = res.data.businessLicense;
        this.form.legalPersonName = res.data.legalPersonName;
        this.form.contactPersonName = res.data.supplierContactPerson;
        this.form.contactPersonPhone = res.data.supplierContactPhone;
        this.form.customerCode = res.data.supplierCode;
        this.form.id = row.id;
        this.dialogMode = "confirm";
        this.aoTuMationDialogVisible = true;
        this.originalFormInDialog = JSON.parse(JSON.stringify(this.form));
      });
    },
    // 确认
    handleConfirm() {
      accApi
        .updateCustomer({
          id: this.form.id,
          supplierCode: this.form.customerCode,
          supplierEntCode: this.form.entCode,
          supplierEntPrefix: this.form.entPrefix,
          supplierContactPerson: this.form.contactPersonName,
          supplierContactPhone: this.form.contactPersonPhone,
          status: 5,
        })
        .then((res) => {
          this.$message({
            message: "审核成功",
            type: "success",
          });
          this.aoTuMationDialogVisible = false;
          this.searchHelper.handleQuery();
        });
    },
    // 删除
    onDeleteClick(row) {
      this.$confirm("是否删除？", "提示", { type: "warning" }).then(() => {
        accApi.delSupplier({ id: row.id }).then((res) => {
          this.$message({
            message: "删除成功",
            type: "success",
          });
          this.searchHelper.handleQuery();
        });
      });
    },
    handleReject() {
      this.$confirm("是否确定拒绝？", "提示", { type: "warning" }).then(() => {
        accApi
          .updateCustomer({
            id: this.form.id,
            status: 4,
          })
          .then((res) => {
            this.$message({
              message: "拒绝成功",
              type: "success",
            });
            this.aoTuMationDialogVisible = false;
            this.searchHelper.handleQuery();
          });
      });
    },
    handleSubmitEdit() {
      accApi
        .editSupplier({
          id: this.form.id,
          supplierCode: this.form.customerCode,
          supplierEntCode: this.form.entCode,
          supplierEntPrefix: this.form.entPrefix,
          supplierContactPerson: this.form.contactPersonName,
          supplierContactPhone: this.form.contactPersonPhone,
        })
        .then((res) => {
          this.$message({
            message: "保存成功",
            type: "success",
          });
          this.aoTuMationDialogVisible = false;
          this.searchHelper.handleQuery();
        });
    },
    onEditClick(row) {
      accApi.querySupplierDetail({ id: row.id }).then((res) => {
        this.form.entPrefix = res.data.supplierEntPrefix;
        this.form.companyName = res.data.supplierEntName;
        this.form.entCode = res.data.supplierEntCode;
        this.form.detailedAddress = res.data.address;
        this.form.businessLicense = res.data.businessLicense;
        this.form.legalPersonName = res.data.legalPersonName;
        this.form.contactPersonName = res.data.supplierContactPerson;
        this.form.contactPersonPhone = res.data.supplierContactPhone;
        this.form.customerCode = res.data.supplierCode;
        this.form.id = row.id;
        this.dialogMode = "edit";
        this.aoTuMationDialogVisible = true;
        this.originalFormInDialog = JSON.parse(JSON.stringify(this.form));
      });
    },
    onAddClick() {
      accApi.queryCompanyInfo({}).then((res) => {
        this.options = res.data;
        this.aoTuMationDialogVisible = true;
        this.dialogMode = "add";
        this.form = {
          entPrefix: "",
          companyName: "",
          entCode: "",
          detailedAddress: "",
          businessLicense: "",
          legalPersonName: "",
          contactPersonName: "",
          contactPersonPhone: "",
          customerCode: "",
        };
        this.companyName = undefined;
      });
    },
    changeCompany(entCode) {
      console.log(entCode, "search company");
      const selectedCompany = this.options.find(
        (company) => company.entCode === entCode
      );
      if (selectedCompany) {
        this.form = {
          entPrefix: selectedCompany.entPrefix,
          companyName: selectedCompany.companyName,
          entCode: selectedCompany.entCode,
          detailedAddress: selectedCompany.detailedAddress,
          businessLicense: selectedCompany.businessLicense,
          legalPersonName: selectedCompany.legalPersonName,
          contactPersonName: selectedCompany.contactPersonName,
          contactPersonPhone: selectedCompany.contactPersonPhone,
          customerCode: selectedCompany.customerCode,
        };
      }
    },
    handleDetailClose() {
      this.showDetail = false;
    },
    handleBeforeClose() {
      console.log("beforeClose", this.form, this.originalFormInDialog);
      const hasChanged = !this.$util.isEqual(
        this.form,
        this.originalFormInDialog
      );
      if (hasChanged) {
        return new Promise((resolve, reject) => {
          this.$confirm(
            "当前页面信息有修改，关闭页面将不保存修改，是否确认关闭？",
            "提示",
            {
              confirmButtonText: "确认",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(() => {
              this.aoTuMationDialogVisible = false;
              resolve(); // 允许关闭
            })
            .catch(() => {
              reject(); // 阻止关闭
            });
        });
      } else {
        this.aoTuMationDialogVisible = false;
        return true; // 不需要确认，直接关闭
      }
    },
    // closeDialog() {
    //   console.log('beforeClose', this.form, this.originalFormInDialog)
    //   const hasChanged = !this.$util.isEqual(this.form, this.originalFormInDialog);
    //   if (hasChanged) {
    //     this.$confirm('当前页面信息有修改，关闭页面将不保存修改，是否确认关闭？', '提示', {
    //       confirmButtonText: '确认',
    //       cancelButtonText: '取消',
    //       type: 'warning'
    //     }).then(() => {
    //       this.aoTuMationDialogVisible = false;
    //     }).catch(() => {
    //       // 用户点击取消，不关闭
    //     });
    //   } else {
    //     this.aoTuMationDialogVisible = false;
    //   }
    // },
  },
};
</script>
<style scoped lang="scss">
.el-select-dropdown__item {
  height: 66px;
}
.info-form {
  padding: 20px;
}

.info-value {
  display: inline-block;
  min-width: 450px;
  vertical-align: middle;
}

.info-img {
  width: 220px;
  height: 160px;
}

.el-form-item {
  margin-bottom: 12px;
}

::v-deep .info-form .el-form-item__label {
  padding-right: 10px;
  font-weight: 500;
  text-align: right !important;
  line-height: 32px;
}

.button-container {
  line-height: 60px;
  text-align: center;
}

::v-deep
  .dialog-style
  .el-dialog__header
  .el-dialog__headerbtn
  .el-dialog__close {
  display: inline-block !important;
}
</style>
