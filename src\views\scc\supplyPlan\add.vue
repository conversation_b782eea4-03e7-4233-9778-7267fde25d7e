<template>
  <div class="app-container">
    <el-container :style="{ height: '100%' }">
      <el-main>
        <el-collapse v-model="collapse">
          <el-collapse-item title="创建发货单" :name="1">
            <el-form
              ref="basic"
              label-position="top"
              :model="basicFormModel"
              :disabled="true"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="客户名称：" prop="customerName">
                    <el-input v-model="basicFormModel.customerName"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="企业前缀：" prop="customerPrefix">
                    <el-input
                      v-model="basicFormModel.customerPrefix"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="货物明细" :name="2">
            <el-table
              :data="renderData"
              class="main-table"
              border
              style="width: 100%"
              :span-method="handleSpanMethod"
              :row-key="getRowKey"
            >
              <!-- 序号 -->
              <el-table-column label="序号" width="60">
                <template slot-scope="scope">
                  <span class="sequence-number">{{ scope.row.mainIndex }}</span>
                </template>
              </el-table-column>

              <!-- 计划编号 -->
              <el-table-column prop="planNo" label="计划编号" width="150">
                <template slot-scope="scope">
                  <span>{{ scope.row.planNo }}</span>
                </template>
              </el-table-column>

              <!-- 添加子项 -->
              <el-table-column label="添加子项" width="80">
                <template slot-scope="scope">
                  <el-button
                    type="primary"
                    class="add-sub-btn"
                    @click="addSubItem(scope.row.originalData)"
                  >
                    添加
                  </el-button>
                </template>
              </el-table-column>

              <!-- 计划交货时间 -->
              <el-table-column
                prop="planDeliveryTime"
                label="计划交货时间"
                width="120"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.planDeliveryTime }}</span>
                </template>
              </el-table-column>

              <!-- 物料名称 -->
              <el-table-column prop="materialName" label="物料名称(号)">
                <template slot-scope="scope">
                  <span>{{ scope.row.materialName }}</span>
                </template>
              </el-table-column>

              <!-- 计划数量 -->
              <el-table-column
                prop="planQuantity"
                label="计划数量(单位)"
                width="150"
              >
                <template slot-scope="scope">
                  <!-- {{ scope.row.unit }} -->
                  <span
                    >{{ scope.row.planQuantity }}{{ scope.row.unitName }}</span
                  >
                </template>
              </el-table-column>

              <!-- 实发数量 -->
              <el-table-column
                prop="actualQuantity"
                label="实发数量(单位)"
                width="160"
              >
                <template slot-scope="scope">
                  <div class="input-group">
                    <el-input
                      v-model="scope.row.actualQuantity"
                      class="quantity-input"
                      size="small"
                      placeholder="请输入"
                      @input="handleActualQuantityInput(scope.row, $event)"
                      @blur="handleActualQuantityBlur(scope.row)"
                    />
                    <div class="unit-text">{{ scope.row.unitName }}</div>
                  </div>
                </template>
              </el-table-column>

              <!-- 工业互联网标识码 -->
              <el-table-column
                prop="identificationCode"
                label="工业互联网标识码"
                min-width="200"
              >
                <template slot-scope="scope">
                  <span v-if="scope.row.identificationCode" class="link-text">{{
                    scope.row.identificationCode
                  }}</span>
                  <span v-else
                    ><el-button type="primary" @click="change(scope.row)"
                      >点击选择</el-button
                    ></span
                  >
                </template>
              </el-table-column>

              <el-table-column prop="herbProductName" label="药材产品名称">
              </el-table-column>

              <el-table-column prop="productionBatchNo" label="生产批号">
              </el-table-column>

              <!-- 操作 -->
              <el-table-column label="操作" width="150">
                <template slot-scope="scope">
                  <el-button
                    type="danger"
                    size="mini"
                    icon="el-icon-delete"
                    @click="deleteSubItem(scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 添加主行按钮 -->
            <demo-block
              message="添加"
              style="margin-top: 15px"
              :icon-class="'el-icon-plus icon-class'"
              @click.native="addMainRow"
            />
          </el-collapse-item>

          <el-collapse-item title="发货信息" :name="3">
            <el-form
              ref="basic"
              label-position="top"
              :model="deliveryFormModel"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="收货地址：" prop="deliveryAddress">
                    <el-select
                      v-model="deliveryFormModel.deliveryAddress"
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="(item, index) in deliveryAddressList"
                        :key="index"
                        :value="item"
                        :label="item"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="运输类型：" prop="transportType">
                    <el-select
                      v-model="deliveryFormModel.transportType"
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option value="1" label="自有车辆" />
                      <el-option value="2" label="三方车辆" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="司机电话：" prop="driverPhone">
                    <el-input
                      v-model="deliveryFormModel.driverPhone"
                      placeholder="请输入"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="车牌号：" prop="licensePlate">
                    <el-input
                      v-model="deliveryFormModel.licensePlate"
                      placeholder="请输入"
                      max-length="20"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="remark" label="备注：">
                    <el-switch
                      v-model="isRemark"
                      active-text="备注给对方"
                      inactive-text=""
                    />
                    <el-input
                      v-model="deliveryFormModel.deliveryRemark"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入备注"
                      max-length="200"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="deliveryImage" label="发货凭证：">
                    <!-- <el-upload
                      ref="uploadRef"
                      class="upload-area"
                      action="#"
                      :http-request="beforeUpload"
                      :auto-upload="true"
                      :show-file-list="false"
                      :limit="3"
                      accept=".jpg,.png"
                      list-type="picture-card"
                      :on-preview="handlePictureCardPreview"
                      :on-remove="handleRemove"
                    >
                      <i class="el-icon-plus"></i>
                      <div class="el-upload__tip" slot="tip">
                        请上传JPG或PNG格式，每张图片大小不超过4M
                        最多可上传3张图片
                      </div>
                    </el-upload> -->
                    <upload-img
                      :limit-count="3"
                      :img-list.sync="deliveryFormModel.deliveryImages"
                    >
                      <template #tip>
                        请上传JPG或PNG格式，每张图片大小不超过4M，
                        最多可上传3张图片
                      </template>
                    </upload-img>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>
        </el-collapse>
      </el-main>
      <el-footer v-if="action === 'confirm'" class="button-container">
        <el-button @click="back">取消</el-button>
        <el-button type="primary" @click="submit(2)">确认发货</el-button>
      </el-footer>
      <el-footer v-else class="button-container">
        <el-button type="primary" @click="submit(2)">直接确认发货</el-button>
        <el-button @click="back">取消</el-button>
        <el-button type="primary" @click="submit(1)">提交</el-button>
      </el-footer>

      <!--      选择关联供货计划-->
      <simple-data-dialog
        v-if="supplyPlanVisible"
        title="选择关联供货计划"
        :visible.sync="supplyPlanVisible"
        size="large"
        :show-close="true"
      >
        <!-- 搜索框 -->
        <data-select
          :search-data.sync="supplySearch"
          @return-search="
            (data) => {
              searchHelper.search(data);
            }
          "
          @return-reset="searchHelper.reset"
        >
        </data-select>
        <data-table
          ref="dataTable"
          :table-data="searchHelper.dataList"
          :column="column"
          :pagination.sync="searchHelper.pagination"
          @selection-change="
            (selection) => {
              this.selectedData = selection;
            }
          "
          :table-option="tableOption"
          @search-event="
            () => {
              searchHelper.handleQuery();
            }
          "
        >
          <template v-slot:planStatus="{ row }">
            <el-tag v-if="row.planStatus == '1'" type="warning">{{
              statusMap[row.planStatus]
            }}</el-tag>
            <el-tag v-if="row.planStatus == '2'" type="">{{
              statusMap[row.planStatus]
            }}</el-tag>
            <el-tag v-if="row.planStatus == '3'" type="success">{{
              statusMap[row.planStatus]
            }}</el-tag>
          </template>
        </data-table>
        <el-footer class="button-container">
          <el-button @click="supplyPlanVisible = false">取消</el-button>
          <el-button type="primary" @click="saveSupply">保存</el-button>
        </el-footer>
      </simple-data-dialog>

      <!--      选择关联标识码-->
      <simple-data-dialog
        v-if="codeVisible"
        title="选择关联标识码"
        :visible.sync="codeVisible"
        size="large"
        :show-close="true"
      >
        <!-- 搜索框 -->
        <data-select
          :search-data.sync="codeSearch"
          @return-search="
            (data) => {
              codeHelper.search(data);
            }
          "
          @return-reset="codeHelper.reset"
        />
        <data-table
          ref="multipleTable"
          class="codeTable"
          :table-data="codeHelper.dataList"
          :column="codeColumn"
          :pagination.sync="codeHelper.pagination"
          :table-option="codeTableOption"
          @search-event="
            () => {
              codeHelper.handleQuery();
            }
          "
        >
          <template v-slot:syncStatus="{ row }">
            <el-tag
              effect="plain"
              :type="typeList[row.syncStatus].type"
              size="medium"
              >{{ typeList[row.syncStatus].text }}</el-tag
            >
          </template>
        </data-table>
        <el-footer class="button-container">
          <el-button @click="codeVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCode">保存</el-button>
        </el-footer>
        <simple-data-dialog
          v-if="codeDetailVisible"
          title="药材赋码详情"
          :visible.sync="codeDetailVisible"
          size="medium"
          :show-close="true"
        >
          <el-form
            ref="basic"
            label-position="top"
            :model="basicDetail"
            :disabled="true"
            :inline="true"
            style="flex: 1"
          >
            <el-form-item
              prop="idisCode"
              label="工业标识码："
              class="el-form-item-width"
            >
              <el-input
                v-model="basicDetail.idisCode"
                readonly
                clearable
                :maxlength="50"
              />
            </el-form-item>
            <el-form-item
              prop="productName"
              label="药材产品名称："
              class="el-form-item-width"
            >
              <el-input
                v-model="basicDetail.productName"
                readonly
                clearable
                placeholder=""
              />
            </el-form-item>

            <el-form-item
              prop="batchNo"
              label="生产批号："
              class="el-form-item-width"
            >
              <el-input
                v-model="basicDetail.batchNo"
                readonly
                clearable
                placeholder=""
              />
            </el-form-item>
            <el-form-item
              prop="category"
              label="展示模板："
              class="el-form-item-width"
            >
              <!-- <el-select
                v-model="basicFormModel.category"
                filterable
                clearable
                readonly
                style="width: 100%"
                placeholder=""
              >
                <el-option
                  v-for="item in productNameArr"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select> -->
              <el-input
                v-model="basicDetail.tenantName"
                readonly
                clearable
                placeholder=""
              />
            </el-form-item>
            <el-form-item
              prop="tenantName"
              label="对应鲜品："
              class="el-form-item-width"
            >
              <el-input
                v-model="basicDetail.product.freshFood"
                readonly
                clearable
                placeholder=""
              />
            </el-form-item>
            <el-form-item
              prop="price"
              label="药材统计单位："
              class="el-form-item-width"
            >
              <el-input
                v-model="basicDetail.product.statisticUnits"
                readonly
                clearable
                placeholder=""
              />
            </el-form-item>
            <el-form-item
              prop="price"
              label="药材别名："
              class="el-form-item-width"
            >
              <el-input
                v-model="basicDetail.product.medicinalAlias"
                readonly
                clearable
                placeholder=""
              />
            </el-form-item>
            <el-form-item
              prop="price"
              label="贮藏条件："
              class="el-form-item-width"
            >
              <el-input
                v-model="basicDetail.product.storageCondition"
                readonly
                clearable
                placeholder=""
              />
            </el-form-item>

            <el-row>
              <el-col :span="24">
                <el-form-item
                  prop="remark"
                  label="产品简介："
                  style="width: 90%"
                >
                  <el-input
                    v-model="basicDetail.product.remark"
                    readonly
                    style="width: 100%"
                    clearable
                    type="textarea"
                    :rows="5"
                    placeholder=""
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </simple-data-dialog>
      </simple-data-dialog>
    </el-container>
  </div>
</template>
<script>
import DemoBlock from "@/components/DemoBlock/index.vue";
import DataTable from "@/components/DataTable/index.vue";
import SimpleDataDialog from "@/components/SimpleDataDialog/index.vue";
import acc from "@/api/acc/acc";
import UploadImg from "@/components/DataDialog/uploadImg";
import DataSelect from "@/components/DataSelect/index.vue";

export default {
  name: "SupplyPlanAdd",
  components: {
    DataSelect,
    UploadImg,
    SimpleDataDialog,
    DataTable,
    DemoBlock,
  },
  data() {
    return {
      deliveryAddressList: [],
      selectedPlanId: [],
      basicDetail: {},
      typeList: {
        10: { type: "warning", text: "未同步" },
        20: { type: "success", text: "已同步" },
        30: { type: "danger", text: "同步异常" },
      },
      statusMap: {
        1: "待发货",
        2: "已发货",
        3: "已收货",
      },
      collapse: [1, 2, 3],
      isRemark: true,
      dialogImageUrl: "",
      dialogVisible: false,
      basicFormModel: {
        customerPrefix: "",
        customerName: "",
        customerCreditCode: "",
      },
      deliveryFormModel: {
        deliveryAddress: "",
        transportType: "",
        transportTypeName: "",
        driverPhone: "",
        licensePlate: "",
        deliveryRemark: "",
        deliveryImages: [],
      },
      tableData: [],
      renderData: [],
      column: {
        data: [
          {
            label: "序号",
            prop: "index",
            width: "80",
            sortable: false,
          },

          {
            label: "计划交货时间",
            prop: "planDeliveryTime",
            width: "200",
            sortable: false,
          },
          {
            label: "计划状态",
            prop: "planStatus",
            width: "200",
            sortable: false,
            slotName: "planStatus",
          },
          {
            label: "计划编号",
            prop: "planCode",
            width: "300",
            sortable: false,
          },
          {
            label: "物料名称(客)",
            prop: "materialName",
            width: "200",
            sortable: false,
          },
          {
            label: "计划数量",
            prop: "planQuantity",
            width: "200",
            sortable: false,
          },
          {
            label: "单位",
            prop: "unitName",
            width: "80",
            sortable: false,
          },
          {
            label: "客户名称",
            prop: "customerName",
            width: "200",
            sortable: false,
          },
          {
            label: "备注",
            prop: "remark",
            sortable: false,
          },
        ],
      },
      tableOption: {
        option: {
          enableSelected: true, // 开启多选
          height: "calc(100vh - 320px)",
        },
        event: {
          selectionChange: this.handleSupplySelection,
          // 事件处理...
        },
      },
      selectedData: [],
      selectedId: [],
      selectedCode: [],
      selectedCodes: {},
      supplyPlanVisible: false,
      searchHelper: new this.$searchHelper({
        api: acc.queryCooperationLList,
        handleSearchParams: this.handleSearchParams,
      }),
      codeHelper: new this.$searchHelper({
        api: acc.tagDetailDateListApi,
        handleSearchParams: this.handleCodeParams,
      }),
      supplySearch: {
        materialName: {
          label: "物料名称",
          value: null,
          type: "input",
          option: {
            placeholder: "请输入物料名称",
          },
        },
        planDeliveryTime: {
          label: "计划交货时间",
          value: null,
          type: "date",
          option: {
            placeholder: "请选择计划交货时间",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
        },
      },
      codeSearch: {
        searchText: {
          label: "工业标识码/关联对象/关联批号",
          value: null,
          type: "input",
          option: {
            placeholder: "请输入工业标识码/关联对象/关联批号",
          },
        },
      },
      codeVisible: false,
      codeDetailVisible: false,
      codeColumn: {
        data: [
          {
            label: "序号",
            prop: "index",
            width: "60px",
            sortable: false,
          },
          {
            label: "工业标识码",
            prop: "idisCode",
          },
          {
            label: "同步状态",
            prop: "syncStatus",
            slotName: "syncStatus",
            width: "100px",
            sortable: false,
          },
          {
            label: "关联对象",
            prop: "productName",
          },
          {
            label: "关联批号",
            prop: "batchNo",
          },
          {
            label: "展示模板",
            prop: "tenantName",
          },
          {
            label: "创建人",
            prop: "userName",
          },
          {
            label: "创建时间",
            prop: "createTime",
          },
        ],
        operation: {
          label: "操作",
          width: "80px",
          data: [
            {
              label: "详情",
              action: this.onCodeClick,
              permission: "all",
            },
          ],
        },
      },
      codeTableOption: {
        option: {
          enableSelected: true, // 开启选择功能
          height: "calc(100vh - 320px)",
          // 禁用全选功能的配置
          selectOnIndeterminate: false, // 禁用全选时的中间状态
          cellClickSelected: false, // 禁用点击行选中功能，只能通过checkbox选择
        },
        event: {
          selectionChange: (selection) => {
            console.log("selectionChange 事件触发:", selection);
            // 简单的单选逻辑：直接更新选中数据
            this.codeSelectedData = selection;
          },
          select: this.codeSelect,
          // 禁用全选事件
          selectAll: () => {
            console.log("selectAll 被禁用，阻止全选操作");
            // 清空所有选择，阻止全选操作
            this.$nextTick(() => {
              this.$refs.multipleTable.clearSelection();
              this.codeSelectedData = [];
            });
          },
        },
      },
      action: "",
      codeSelectedData: [],
      currentEditingRow: null, // 当前正在编辑的行
      nextItemId: 6, // 下一个项目的ID计数器
    };
  },
  created() {
    // 获取路由的query
    const { action, companyName, entPrefix, entCode, batchPlanCodes } =
      this.$route.query;
    this.basicFormModel.customerName = companyName;
    this.basicFormModel.customerPrefix = entPrefix;
    this.basicFormModel.customerCreditCode = entCode;
    this.action = action;

    // 处理批量选中的计划编号
    if (batchPlanCodes) {
      const planCodeArray = batchPlanCodes.split(",");
      this.selectedPlanId = planCodeArray;
      console.log("批量发货 - 接收到的计划编号:", planCodeArray);
      // 自动加载批量选中的计划数据
      this.loadBatchSelectedPlans(planCodeArray);
    }

    // 验证初始数据
    // this.validateTableData();
    if (action === "edit" || action === "confirm") {
      acc
        .queryDeliveryOrderDetail({
          deliveryOrderNo: this.$route.query.deliveryOrderNo,
        })
        .then((res) => {
          this.basicFormModel.customerName = res.data.customerName;
          this.basicFormModel.customerPrefix = res.data.customerPrefix;
          this.basicFormModel.customerCreditCode = res.data.customerCreditCode;
          this.selectedPlanId = res.data.planIds || [];
          this.deliveryFormModel.deliveryAddress = res.data.deliveryAddress;
          this.deliveryFormModel.transportType = res.data.transportType;
          this.deliveryFormModel.deliveryRemark = res.data.deliveryRemark;
          this.deliveryFormModel.transportTypeName = res.data.transportTypeName;
          this.deliveryFormModel.driverPhone = res.data.driverPhone;
          this.deliveryFormModel.licensePlate = res.data.licensePlate;
          this.isRemark = res.data.deliveryRemarkEnabled == "1" ? true : false;
          this.tableData = this.convertToFlatFormat(res.data.details);
          this.deliveryFormModel.deliveryImages = (
            res.data.deliveryImages || []
          ).map((item) => ({
            url: item,
          }));
          // 处理表格数据
          this.processTableData();
        });
    } else {
      this.processTableData();
    }
  },
  methods: {
    // 加载批量选中的计划数据
    async loadBatchSelectedPlans(planCodeArray) {
      try {
        console.log("开始加载批量选中的计划数据:", planCodeArray);

        // 调用 queryCooperationLList API，传入 planIds 参数
        const response = await acc.queryDeliveryPlanAll({
          planIds: planCodeArray,
          // status: 1,
          // deliverOrderNull: 2,
          // customerEntCode: this.basicFormModel.customerCreditCode,
        });

        if (response && response.data && response.data.length > 0) {
          console.log("批量计划数据加载成功:", response.data);

          // 模拟选中数据，调用 saveSupply 方法进行回显（不显示添加成功消息）
          this.selectedData = response.data;
          this.selectedId = response.data.map((item) => item.planCode);
          this.saveSupply(false);

          this.$message.success(`成功加载 ${response.data.length} 个供货计划`);
        } else {
          console.warn("未找到匹配的计划数据");
          this.$message.warning("未找到匹配的供货计划数据");
        }
      } catch (error) {
        console.error("加载批量计划数据失败:", error);
        this.$message.error("加载供货计划数据失败，请重试");
      }
    },

    // 表格查询条件
    handleSearchParams(params) {
      return Object.assign({}, params, {
        status: 1,
        filterPlanCodeList: this.selectedId,
        deliverOrderNull: 2,
        customerEntCode: this.basicFormModel.customerCreditCode,
      });
    },
    handleCodeParams(params) {
      return Object.assign({}, params, {
        syncStatus: 20,
        filterIdisCode: this.selectedCodes[this.currentEditingRow.planNo],
      });
    },
    onCodeClick(row) {
      acc.detailTagDetailApi({ id: row.id }).then((res) => {
        this.basicDetail = res.data;
        this.codeDetailVisible = true;
      });
    },
    // 处理实发数量输入
    handleActualQuantityInput(row, value) {
      console.log("输入处理 - 原始值:", value, "行数据:", row);

      // 只允许输入数字和小数点
      const cleanValue = value.replace(/[^\d.]/g, "");

      // 防止多个小数点
      const parts = cleanValue.split(".");
      let processedValue = cleanValue;

      if (parts.length > 2) {
        processedValue = parts[0] + "." + parts.slice(1).join("");
      }

      // 限制小数位数为2位
      if (parts[1] && parts[1].length > 2) {
        processedValue = parts[0] + "." + parts[1].substring(0, 2);
      }

      // 确保数据同步到 tableData 中对应的项目
      this.updateActualQuantityInTableData(row, processedValue);

      console.log("输入处理 - 处理后值:", processedValue);
    },

    // 处理实发数量失焦事件
    handleActualQuantityBlur(row) {
      console.log("失焦验证 - 当前值:", row.actualQuantity, "行数据:", row);

      if (row.actualQuantity) {
        const numValue = parseFloat(row.actualQuantity);

        // 验证是否为有效数字且大于0
        if (isNaN(numValue) || numValue <= 0) {
          this.$message.warning("实发数量必须是大于0的数字");
          this.updateActualQuantityInTableData(row, "");
          return;
        }

        // 格式化数字，保留有效的小数位
        const formattedValue = numValue.toString();
        this.updateActualQuantityInTableData(row, formattedValue);

        console.log("失焦验证 - 格式化后值:", formattedValue);
      }
    },

    // 更新 tableData 中对应项目的实发数量
    updateActualQuantityInTableData(row, value) {
      // 通过 ID 找到 tableData 中对应的项目并更新
      const targetIndex = this.tableData.findIndex(
        (item) => item.id === row.id
      );
      if (targetIndex !== -1) {
        // 使用 Vue.set 确保响应式更新
        this.$set(this.tableData[targetIndex], "actualQuantity", value);
        // 同时更新当前行对象
        this.$set(row, "actualQuantity", value);

        console.log("数据更新 - tableData索引:", targetIndex, "新值:", value);
        console.log(
          "数据更新 - 更新后的tableData项:",
          this.tableData[targetIndex]
        );
      } else {
        console.warn("未找到对应的tableData项目，ID:", row.id);
      }
    },

    // 验证实发数量格式
    validateActualQuantity(value) {
      if (!value) return false;

      const numValue = parseFloat(value);
      if (isNaN(numValue) || numValue <= 0) {
        return false;
      }

      // 检查小数位数不超过2位
      const decimalPart = value.toString().split(".")[1];
      if (decimalPart && decimalPart.length > 2) {
        return false;
      }

      return true;
    },

    // 数据验证方法
    validateTableData() {
      const errors = [];
      // const idSet = new Set();

      this.tableData.forEach((item, index) => {
        // 检查ID唯一性
        // if (idSet.has(item.id)) {
        //   errors.push(`第${index + 1}行：ID "${item.id}" 重复`);
        // } else {
        //   idSet.add(item.id);
        // }

        // 检查必填字段
        if (!item.identificationCode) {
          errors.push(`第${index + 1}行：工业互联网标识码不能为空`);
        }
        if (!item.herbProductName) {
          errors.push(`第${index + 1}行：药材产品名称不能为空`);
        }
        if (!item.productionBatchNo) {
          errors.push(`第${index + 1}行：生产批号不能为空`);
        }
        // if (!item.planQuantity || item.planQuantity <= 0) {
        //   errors.push(`第${index + 1}行：计划数量必须大于0`);
        // }

        // 检查实发数量格式（如果已填写）
        if (
          item.actualQuantity &&
          !this.validateActualQuantity(item.actualQuantity)
        ) {
          errors.push(
            `第${
              index + 1
            }行：实发数量格式不正确，必须是大于0的数字（最多两位小数）`
          );
        }
      });

      if (errors.length > 0) {
        console.warn("数据验证失败:", errors);
        return false;
      }
      return true;
    },

    // 生成唯一ID的工具方法
    generateUniqueId() {
      return `item_${Date.now()}_${Math.random()
        .toString(36)
        .substring(2, 11)}`;
    },

    // 将一维数组转换为二维数据格式（按planNo分组）
    convertToGroupedFormat(flatArray) {
      if (!Array.isArray(flatArray) || flatArray.length === 0) {
        return [];
      }

      // 按planNo分组
      const groupedData = {};

      flatArray.forEach((item) => {
        const planNo = item.planNo;

        if (!groupedData[planNo]) {
          // 创建新的分组
          groupedData[planNo] = {
            materialCode: item.materialCode || "",
            materialName: item.materialName || "",
            planDeliveryTime: item.planDeliveryTime || "",
            planNo: planNo,
            planQuantity: item.planQuantity || undefined,
            deliveryOrderHerbInfoVOList: [],
            unit: item.unit || "",
            unitName: item.unitName || "",
          };
        }

        // 添加到对应分组的子项列表
        groupedData[planNo].deliveryOrderHerbInfoVOList.push({
          actualQuantity: item.actualQuantity || undefined,
          deliveryOrderNo: item.deliveryOrderNo || "",
          herbProductCode: item.herbProductCode || "",
          herbProductName: item.herbProductName || "",
          // id: item.id || 0,
          identificationCode: item.identificationCode || "",
          planNo: planNo,
          unit: item.unit || "",
          unitName: item.unitName || "",
          productionBatchNo: item.productionBatchNo || "",
        });
      });

      // 转换为数组格式
      return Object.values(groupedData);
    },

    // 将二维数据格式转换为一维数组
    convertToFlatFormat(groupedArray) {
      if (!Array.isArray(groupedArray) || groupedArray.length === 0) {
        return [];
      }

      const flatArray = [];

      groupedArray.forEach((group) => {
        if (
          group.deliveryOrderHerbInfoVOList &&
          Array.isArray(group.deliveryOrderHerbInfoVOList)
        ) {
          // ==
          this.selectedCodes[group.planNo] =
            group.deliveryOrderHerbInfoVOList.map(
              (item) => item.identificationCode
            );
          group.deliveryOrderHerbInfoVOList.forEach((item) => {
            flatArray.push({
              id: item.id || "",
              planNo: group.planNo || "",
              planDeliveryTime: group.planDeliveryTime || "",
              materialName: group.materialName || "",
              materialCode: group.materialCode || "",
              planQuantity: group.planQuantity || undefined,
              unit: item.unit || "",
              unitName: item.unitName || "",
              actualQuantity: item.actualQuantity || undefined,
              identificationCode: item.identificationCode || "",
              productName: group.materialName || "",
              herbProductName: item.herbProductName || "",
              herbProductCode: item.herbProductCode || "",
              productionBatchNo: item.productionBatchNo || "",
              deliveryOrderNo: item.deliveryOrderNo || "",
            });
          });
        }
      });

      return flatArray;
    },

    // 处理表格数据，为合并单元格做准备
    async processTableData() {
      const response = await acc.queryDeliveryPlanAll({
        planIds: this.selectedPlanId,
      });
      this.deliveryAddressList = [
        ...new Set(response.data.map((item) => item.receiveAddress)),
      ];
      // 保持原有顺序，不重新分组排序
      this.renderData = [];
      let mainIndex = 1;
      const planNumberIndexMap = {}; // 记录每个计划编号的主序号

      // 按原有顺序处理数据
      this.tableData.forEach((item, index) => {
        // 检查是否是该计划编号的第一次出现
        const isFirstInGroup = !(item.planNo in planNumberIndexMap);

        if (isFirstInGroup) {
          planNumberIndexMap[item.planNo] = mainIndex;
          mainIndex++;
        }

        // 计算该计划编号的组大小
        const groupSize = this.tableData.filter(
          (data) => data.planNo === item.planNo
        ).length;

        // 计算在组内的索引
        const groupIndex =
          this.tableData
            .slice(0, index + 1)
            .filter((data) => data.planNo === item.planNo).length - 1;

        this.renderData.push({
          ...item,
          uniqueKey: `${item.planNo}_${index}`, // 使用原始索引确保唯一性
          mainIndex: isFirstInGroup ? planNumberIndexMap[item.planNo] : "", // 只有第一行显示主序号
          isFirstInGroup: isFirstInGroup,
          groupSize: groupSize,
          groupIndex: groupIndex,
          originalIndex: index,
          originalData: item,
        });
      });
    },

    // 为表格行生成唯一key，避免DOM复用导致数据错乱
    getRowKey(row) {
      return row.uniqueKey;
    },

    // 处理单元格合并
    handleSpanMethod({ row, columnIndex }) {
      // 需要合并的列索引：0-序号, 1-计划编号, 2-添加子项, 3-计划交货时间, 4-物料名称, 5-计划数量
      const mergeColumns = [0, 1, 2, 3, 4, 5];

      if (mergeColumns.includes(columnIndex)) {
        if (row.isFirstInGroup) {
          // 如果是组内第一行，返回合并的行数
          return {
            rowspan: row.groupSize,
            colspan: 1,
          };
        } else {
          // 如果不是组内第一行，隐藏该单元格
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }

      // 其他列不合并
      return {
        rowspan: 1,
        colspan: 1,
      };
    },

    // 添加主行
    addMainRow() {
      this.searchHelper.handleQuery();
      this.supplyPlanVisible = true;
    },

    // 添加子项(实发数量)
    addSubItem(originalData) {
      console.log(originalData, "originalData");

      // 创建新的子项数据，分配唯一ID
      const newSubItem = {
        ...originalData,
        id: this.generateUniqueId(), // 使用新的ID生成方法
        actualQuantity: "", // 初始化实发数量为空字符串，确保字段存在
        identificationCode: "", // 清空标识码，需要重新选择
        herbProductName: "",
        productionBatchNo: "",
        // productName: originalData.productName || originalData.materialName,
        // batchNo: originalData.batchNo || "",
      };

      // 找到同一计划编号的最后一个位置，插入到其后面
      let insertIndex = this.tableData.length;
      for (let i = this.tableData.length - 1; i >= 0; i--) {
        if (this.tableData[i].planNo === originalData.planNo) {
          insertIndex = i + 1;
          break;
        }
      }

      // 插入到指定位置，保持同一计划编号的项目聚集在一起
      this.tableData.splice(insertIndex, 0, newSubItem);

      // 重新处理表格数据
      this.processTableData();

      this.$message.success("子项添加成功");
    },

    // 删除子项
    deleteSubItem(row) {
      this.$confirm("确定要删除这个子项吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 使用ID进行精确匹配删除，保持顺序不变
          const targetIndex = this.tableData.findIndex(
            (item) => item.id === row.id
          );

          if (targetIndex !== -1) {
            const deletedItem = this.tableData[targetIndex];

            // 从原始数据中删除
            this.tableData.splice(targetIndex, 1);

            // 处理 selectedId 和 selectedCode 数组
            this.cleanupSelectedArrays(deletedItem);

            // 重新处理表格数据
            this.processTableData();

            this.$message.success("删除成功");
          } else {
            this.$message.error("删除失败，未找到对应数据");
          }
        })
        .catch(() => {
          // 取消删除
        });
    },

    // 清理选中的ID和标识码数组
    cleanupSelectedArrays(deletedItem) {
      // 检查是否还有其他项目使用相同的planNo
      const remainingItemsWithSamePlan = this.tableData.filter(
        (item) => item.planNo === deletedItem.planNo
      );

      // 如果没有其他项目使用相同的planNo，从selectedId中移除
      if (remainingItemsWithSamePlan.length === 0 && deletedItem.planNo) {
        const planIndex = this.selectedId.indexOf(deletedItem.planNo);
        if (planIndex !== -1) {
          this.selectedId.splice(planIndex, 1);
          console.log(`从 selectedId 中移除: ${deletedItem.planNo}`);
        }
      }

      // 检查是否还有其他项目使用相同的标识码
      if (deletedItem.identificationCode) {
        const remainingItemsWithSameCode = this.tableData.filter(
          (item) => item.identificationCode === deletedItem.identificationCode
        );

        // 如果没有其他项目使用相同的标识码，从selectedCode中移除
        if (remainingItemsWithSameCode.length === 0) {
          const codeIndex = this.selectedCode.indexOf(
            deletedItem.identificationCode
          );
          if (codeIndex !== -1) {
            this.selectedCode.splice(codeIndex, 1);
            console.log(
              `从 selectedCode 中移除: ${deletedItem.identificationCode}`
            );
          }
        }
      }

      console.log("清理后的 selectedId:", this.selectedId);
      console.log("清理后的 selectedCode:", this.selectedCode);
    },

    beforeUpload(file) {
      console.log(file);
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    back() {
      this.$router.replace("supplyPlan");
    },
    submit(type) {
      console.log(this.tableData, "-------tableData-------");
      const deliveryImages = this.deliveryFormModel.deliveryImages.map(
        (item) => item.url
      );
      // 1 保存 2发货
      const tmp = {
        ...this.basicFormModel,
        ...this.deliveryFormModel,
        type,
        details: this.convertToGroupedFormat(this.tableData),
        deliveryRemarkEnabled: this.isRemark ? 1 : 0,
        deliveryImages,
      };
      console.log(tmp, "-------tmp-------");
      let api = null;
      if (this.$route.query.deliveryOrderNo) {
        api = acc.updateDeliveryOrder;
        tmp.deliveryOrderNo = this.$route.query.deliveryOrderNo;
      } else {
        api = acc.createDeliveryOrder;
      }
      if (type === 1) {
        api(tmp).then((res) => {
          this.$message.success("保存发货单成功");
          this.$router.replace({
            path: "supplyPlan",
            query: { activeName: "second" },
          });
        });
      } else {
        // 提交前验证数据
        if (!this.validateTableData()) {
          this.$message.error("请选择工业互联网标识码");
          return;
        }
        // 检查是否有实发数量为空或格式不正确的项目
        const invalidActualQuantity = this.tableData.filter(
          (item) =>
            !item.actualQuantity ||
            !this.validateActualQuantity(item.actualQuantity)
        );
        if (invalidActualQuantity.length > 0) {
          this.$message.warning(
            "请填写所有项目的实发数量，且必须是大于0的数字（最多两位小数）"
          );
          return;
        }
        this.$confirm(
          `${
            this.action === "confirm"
              ? "确认发货后，关联供货计划将转为“已发货”，且明细信息不可再次编辑，是否确定直接确认发货?"
              : "直接确认发货后，关联供货计划将转为“已发货”，且明细信息不可再次编辑，是否确定直接确认发货？"
          }`,
          "",
          {
            type: "warning",
          }
        ).then(() => {
          api(tmp).then((res) => {
            this.$message.success("发货成功");
            this.$router.replace({
              path: "supplyPlan",
              query: { activeName: "second" },
            });
          });
        });
      }
    },
    // 选择工业互联网标识码
    change(row) {
      console.log("change 方法被调用，当前行:", row);
      // 保存当前选择的行信息，用于后续更新
      this.currentEditingRow = row;
      console.log("设置 currentEditingRow:", this.currentEditingRow);

      // 清空之前的选择
      this.codeSelectedData = [];
      if (!Object.hasOwn(this.selectedCodes, row.planNo)) {
        this.selectedCodes[row.planNo] = [];
      }
      // 查询标识码数据
      this.codeHelper.handleQuery();
      this.codeVisible = true;
    },
    async saveSupply(showMessage = true) {
      console.log("saveSupply 被调用，数据:", this.selectedData);
      if (this.selectedData && this.selectedData.length > 0) {
        // 将选中的供货计划数据添加到tableData中
        this.selectedData.forEach((item) => {
          // 检查是否已经存在相同的计划编号，避免重复添加
          // const existingItem = this.tableData.find(
          //   (existing) => existing.planNo === item.planCode
          // );
          // if (!existingItem) {

          //   console.log("添加计划到表格:", newItem);
          // } else {
          //   console.log("计划已存在，跳过添加:", item.planCode);
          // }
          const newItem = {
            id: item.id, // 分配唯一ID
            planNo: item.planCode,
            planDeliveryTime: item.planDeliveryTime,
            materialName: item.materialName,
            materialCode: item.materialCode || "",
            planQuantity: item.planQuantity,
            unit: item.unit,
            unitName: item.unitName,
            actualQuantity: "", // 初始化实发数量为空字符串，确保字段存在且可编辑
            identificationCode: "",
            productName: item.materialName,
            // batchNo: "",
            herbProductName: "",
            productionBatchNo: "",
            deliveryOrderNo: "",
          };
          this.tableData.push(newItem);

          // 只有当 selectedId 中不存在时才添加
          if (!this.selectedId.includes(item.planCode)) {
            this.selectedId.push(item.planCode);
          }
          if (!this.selectedPlanId.includes(item.id)) {
            this.selectedPlanId.push(item.id);
          }
        });

        // 重新处理表格数据
        this.processTableData();

        // 只有在手动选择时才显示成功消息，批量加载时不显示
        if (showMessage) {
          this.$message.success("供货计划添加成功");
        }
      }

      this.supplyPlanVisible = false;
      this.selectedData = [];
    },

    // 保存选择的标识码
    saveCode() {
      console.log("saveCode 方法被调用");
      console.log("codeSelectedData:", this.codeSelectedData);
      console.log("currentEditingRow:", this.currentEditingRow);

      if (
        this.codeSelectedData &&
        this.codeSelectedData.length > 0 &&
        this.currentEditingRow
      ) {
        const selectedCode = this.codeSelectedData[0];
        console.log("选中的标识码:", selectedCode);
        console.log(
          this.tableData,
          "========================tableData=========================="
        );

        // 使用ID进行精确匹配
        const targetIndex = this.tableData.findIndex(
          (item) => item.id === this.currentEditingRow.id
        );

        if (targetIndex !== -1) {
          // 更新标识码和相关信息
          this.tableData[targetIndex].identificationCode =
            selectedCode.idisCode; // 同时更新identificationCode字段
          this.tableData[targetIndex].herbProductName =
            selectedCode.productName;
          this.tableData[targetIndex].productionBatchNo = selectedCode.batchNo;

          // 添加到已选择的标识码列表
          if (
            !this.selectedCodes[this.currentEditingRow.planNo].includes(
              selectedCode.idisCode
            )
          ) {
            this.selectedCodes[this.currentEditingRow.planNo].push(
              selectedCode.idisCode
            );
          }

          // 重新处理表格数据
          this.processTableData();

          this.$message.success("标识码设置成功");
        } else {
          this.$message.error("未找到对应的数据行");
        }
      } else {
        // 处理没有选中数据的情况
        if (!this.codeSelectedData || this.codeSelectedData.length === 0) {
          this.$message.warning("请先选择一个标识码");
          return;
        }
        if (!this.currentEditingRow) {
          this.$message.warning("当前编辑行信息丢失，请重新操作");
          return;
        }
      }

      this.codeVisible = false;
      this.codeSelectedData = [];
      this.currentEditingRow = null;
    },
    // 供货计划选择列
    handleSupplySelection(row) {
      this.selectedData = row;
    },
    handleCodeSelection(row) {
      console.log(row);
    },
    codeSelect(selection, row) {
      console.log("codeSelect 被调用 - checkbox点击");
      console.log("当前点击行:", row);
      console.log("当前选择列表:", selection);

      // 检查当前行是否在选择列表中
      const isCurrentRowSelected = selection.some((item) => item.id === row.id);

      if (isCurrentRowSelected) {
        // 当前行被选中
        if (selection.length > 1) {
          // 如果选择了多行，清除所有选择，只保留当前行
          console.log("检测到多选，执行单选逻辑 - 只保留当前行");
          this.$nextTick(() => {
            this.$refs.multipleTable.clearSelection();
            this.$refs.multipleTable.toggleRowSelection([row], true);
          });
        } else {
          // 只选择了当前行，正常情况
          console.log("单选状态正常");
        }
      } else {
        // 当前行未被选中，说明是取消选择或者选择了其他行
        if (selection.length > 0) {
          // 选择了其他行，清除所有选择，只选择当前行
          console.log("选择了其他行，切换到当前行");
          this.$nextTick(() => {
            this.$refs.multipleTable.clearSelection();
            this.$refs.multipleTable.toggleRowSelection([row], true);
          });
        } else {
          // 取消选择
          console.log("取消选择");
        }
      }

      console.log("codeSelect 处理完成");
    },
  },
};
</script>

<style scoped lang="scss">
.input-group {
  display: flex;
}
.unit-text {
  display: inline-block;
  width: 50px;
  line-height: 32px;
  margin-left: 10px;
}
.button-container {
  line-height: 60px;
  text-align: center;
}

// 表格样式优化
.main-table {
  .sequence-number {
    font-weight: bold;
    color: #409eff;
  }

  .add-sub-btn {
    font-size: 12px;
  }

  .link-text {
    color: #409eff;
    cursor: pointer;
    text-decoration: underline;

    &:hover {
      color: #66b1ff;
    }
  }

  // 合并单元格的边框处理
  ::v-deep .el-table__body tr td {
    border-right: 1px solid #ebeef5;
  }

  // 子项行的样式区分
  ::v-deep .el-table__body tr:not(:first-child) {
    background-color: #fafafa;
  }
}

// 禁用标识码选择表格的全选功能
::v-deep .codeTable {
  .el-table
    .el-table__header-wrapper
    .el-table__header
    .el-table-column--selection
    .cell
    .el-checkbox {
    // 完全隐藏全选checkbox
    display: none !important;
  }
}

// 禁用全选checkbox的点击事件（双重保险）
::v-deep .codeTable {
  .el-table
    .el-table__header-wrapper
    .el-table__header
    .el-table-column--selection
    .cell {
    pointer-events: none !important;
  }
}

// 确保行选择checkbox正常显示和工作
::v-deep .codeTable {
  .el-table .el-table__body .el-table-column--selection .cell .el-checkbox {
    display: block !important;
    pointer-events: auto !important;
  }
}
</style>
