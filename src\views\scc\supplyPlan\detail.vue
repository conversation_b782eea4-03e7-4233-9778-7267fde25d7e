<template>
  <div class="app-container">
    <el-container :style="{ height: '100%' }">
      <el-main>
        <el-collapse v-model="collapse">
          <el-collapse-item :name="1">
            <template slot="title">
              供货计划详情<span class="title-code">{{
                basicFormModel.planCode
              }}</span>
            </template>
            <el-form
              ref="basic"
              label-position="top"
              :model="basicFormModel"
              :rules="basicFormRules"
              :disabled="true"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="purchaseOrderNo" label="关联采购单号：">
                    <el-input
                      v-model="basicFormModel.purchaseOrderNo"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="planCode" label="计划编号：">
                    <el-input
                      v-model="basicFormModel.planCode"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="materialName" label="物料名称：">
                    <el-input
                      v-model="basicFormModel.materialName"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="materialCode" label="物料编码：">
                    <el-input
                      v-model="basicFormModel.materialCode"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="planQuantity" label="计划数量：">
                    <el-input
                      v-model="basicFormModel.planQuantity"
                      style="width: 90%"
                      placeholder=""
                    />
                    <span style="position: absolute; margin-left: 10px">
                      {{ basicFormModel.unitName || "kg" }}
                    </span>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item prop="planDeliveryTime" label="计划交货时间：">
                    <el-date-picker
                      v-model="basicFormModel.planDeliveryTime"
                      type="date"
                      value-format="yyyy-MM-dd"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="supplierName" label="供应商名称：">
                    <el-input
                      v-model="basicFormModel.supplierName"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="entPrefix" label="企业前缀：">
                    <el-input
                      v-model="basicFormModel.entPrefix"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="receiveAddress" label="收货地址：">
                    <el-input
                      v-model="basicFormModel.receiveAddress"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="remark" label="计划备注：">
                    <el-input
                      v-model="basicFormModel.remark"
                      type="textarea"
                      :rows="3"
                      placeholder=""
                      max-length="100"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>

          <el-collapse-item
            v-if="planStatus !== '1'"
            title="发货信息"
            :name="2"
          >
            <span class="table-title" style="text-align: center"
              >本计划发货明细</span
            >
            <el-table
              ref="table"
              border
              :data="detailTableData"
              show-summary
              :summary-method="customSummaryMethod"
              summary-cell-class-name="bold-summary"
            >
              <template v-for="(item, index) in detailColumns.data">
                <el-table-column
                  :prop="item.prop"
                  :label="item.label"
                  :key="index"
                >
                  <template slot-scope="scope">
                    <span v-if="item.prop === 'index'">{{
                      scope.$index + 1
                    }}</span>
                    <span
                      v-else-if="item.prop === 'actualQuantity'"
                      style="
                        text-align: right;
                        display: inline-block;
                        width: 100%;
                      "
                    >
                      {{ scope.row[item.prop] }}
                      {{ basicFormModel.unitName || "kg" }}
                    </span>
                    <span v-else>{{ scope.row[item.prop] }}</span>
                  </template>
                </el-table-column>
              </template>
            </el-table>

            <el-form
              label-position="top"
              :model="orderDetailFormModel"
              :disabled="true"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="totalActualQuantity" label="实发总数量：">
                    <el-input
                      v-model="orderDetailFormModel.totalActualQuantity"
                      style="width: 90%"
                      placeholder=""
                    />
                    <span style="position: absolute; margin-left: 10px">
                      {{ basicFormModel.unitName || "kg" }}
                    </span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="deliveryTime" label="发货时间：">
                    <el-date-picker
                      v-model="orderDetailFormModel.deliveryTime"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="transportType" label="运输类型：">
                    <el-input
                      v-model="orderDetailFormModel.transportType"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="driverPhone" label="司机电话：">
                    <el-input
                      v-model="orderDetailFormModel.driverPhone"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="licensePlate" label="车牌号：">
                    <el-input
                      v-model="orderDetailFormModel.licensePlate"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="deliveryRemark" label="发货备注：">
                    <el-input
                      v-model="orderDetailFormModel.deliveryRemark"
                      type="textarea"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="delivery_image" label="图片：">
                    <div class="image-list">
                      <el-image
                        v-for="(url, index) in deliveryImages"
                        :key="index"
                        :src="url"
                        :preview-src-list="deliveryImages"
                        style="width: 150px; height: 150px; margin-right: 10px"
                        fit="cover"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>

          <el-collapse-item
            v-if="planStatus === '3'"
            title="收货信息"
            :name="3"
          >
            <el-form
              v-if="flag === 'detail'"
              label-position="top"
              :model="takeDeliveryFormModel"
              :disabled="true"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="receiptQuantity" label="实收数量：">
                    <el-input
                      v-model="takeDeliveryFormModel.receiptQuantity"
                      style="width: 90%"
                      placeholder=""
                    />
                    <span style="position: absolute; margin-left: 10px">
                      {{ basicFormModel.unitName || "kg" }}
                    </span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="receiptOperator" label="收货作业人：">
                    <el-input
                      v-model="takeDeliveryFormModel.receiptOperator"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="receiptTime" label="收货时间：">
                    <el-date-picker
                      v-model="takeDeliveryFormModel.receiptTime"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="receiptRemark" label="收货备注：">
                    <el-input
                      v-model="takeDeliveryFormModel.receiptRemark"
                      type="textarea"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="receipt_image" label="图片：">
                    <div class="image-list">
                      <el-image
                        v-for="(url, index) in receiptImages"
                        :key="index"
                        :src="url"
                        :preview-src-list="receiptImages"
                        style="width: 150px; height: 150px; margin-right: 10px"
                        fit="cover"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>

            <el-form
              v-else
              ref="operationForm"
              label-position="top"
              :model="operationFormModel"
              :rules="operationFormRules"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="receiptQuantity" label="实收数量：">
                    <el-input
                      v-model="operationFormModel.receiptQuantity"
                      style="width: 90%"
                      placeholder="请输入实收数量"
                    />
                    <span style="position: absolute; margin-left: 10px">
                      {{ basicFormModel.unitName || "kg" }}
                    </span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>
        </el-collapse>
      </el-main>
      <el-footer class="button-container">
        <el-button @click="back">返回</el-button>
        <!-- <el-button type="primary" @click="save">确认收货</el-button> -->
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import acc from "@/api/acc/acc";

export default {
  name: "SupplyPlanDetail",
  data() {
    return {
      planStatus: "",
      collapse: [1, 2, 3],
      flag: "",
      basicFormModel: {
        planCode: "", // 计划编号
        purchaseOrderNo: "", // 关联采购单号
        supplierName: "", // 供应商名称
        unitName: "", // 单位
        entPrefix: "", // 供应商企业前缀
        materialCode: "", // 物料编码
        materialName: "", // 物料名称
        planQuantity: "", // 计划数量
        planDeliveryTime: "", // 计划交货时间
        remark: "", // 计划备注
        remarkSwitch: 0, // 计划备注开关0关闭 1开启
        receiveAddress: "", // 收货地址
        statisticType: "1", // 保留原有字段
      },
      basicFormRules: {},
      detailColumns: {
        data: [
          {
            prop: "index",
            label: "序号",
            sortable: false,
          },
          {
            label: "标识码",
            prop: "identificationCode",
            sortable: false,
          },
          {
            label: "药材名称",
            prop: "herbProductName",
            sortable: false,
          },
          {
            label: "生产批号",
            prop: "productionBatchNo",
            sortable: false,
          },
          {
            label: "实发数量",
            prop: "actualQuantity",
            slotName: "actualQuantity",
            sortable: false,
          },
        ],
      },
      detailTableData: [],
      orderDetailFormModel: {
        deliveryOrderNo: "",
        totalActualQuantity: "", // 实发总数量
        deliveryTime: "", // 发货时间
        transportType: "", // 运输类型（1自有车辆 2第三方车辆）
        driverPhone: "", // 司机电话
        licensePlate: "", // 车牌号
        deliveryRemark: "", // 发货备注
        deliveryRemarkEnabled: "0", // 发货备注开关：0-关闭，1-开启
      },
      deliveryImages: [],
      receiptImages: [],
      takeDeliveryFormModel: {
        receiptQuantity: "", // 实收数量
        receiptOperator: "", // 收货作业人
        receiptTime: "", // 收货时间
        receiptRemark: "", // 收货备注
      },
      operationFormModel: {
        receiptQuantity: "", // 实收数量
      },
      operationFormRules: {
        receiptQuantity: [{ required: true, message: "请输入实收数量" }],
      },
      // 药材列表数据
      herbList: [],
    };
  },
  mounted() {
    // 确保汇总行样式生效
    this.$nextTick(() => {
      setTimeout(() => {
        // 直接查找并设置汇总行样式
        const summaryRows = document.querySelectorAll(
          ".el-table__footer tr td"
        );
        summaryRows.forEach((cell) => {
          cell.style.fontWeight = "bold";
          cell.style.textAlign = "right";
        });
      }, 100);
    });
    this.flag = this.$route.query.action;
    // 详情
    acc
      .getDeliveryPlanDetail({ id: this.$route.query.id })
      .then((res) => {
        if (res.data) {
          const data = res.data;
          this.planStatus = data.planStatus;
          // 绑定基本信息
          this.basicFormModel = {
            planCode: data.planCode || "",
            purchaseOrderNo: data.purchaseOrderNo || "",
            supplierName: data.supplierName || "",
            unitName: data.unitName || "",
            entPrefix: data.entPrefix || "",
            materialCode: data.materialCode || "",
            materialName: data.materialName || "",
            planQuantity: data.planQuantity || "",
            planDeliveryTime: data.planDeliveryTime || "",
            remark: data.remark || "",
            remarkSwitch: data.remarkSwitch || 0,
            receiveAddress: data.receiveAddress || "",
            statisticType: "1", // 保留原有字段
          };

          // 绑定发货信息
          this.orderDetailFormModel = {
            deliveryOrderNo: "",
            totalActualQuantity: data.totalActualQuantity || "",
            deliveryTime: data.deliveryTime || "",
            transportType: this.getTransportTypeText(data.transportType),
            driverPhone: data.driverPhone || "",
            licensePlate: data.licensePlate || "",
            deliveryRemark: data.deliveryRemark || "",
            deliveryRemarkEnabled: data.deliveryRemarkEnabled || "0",
          };

          // 绑定收货信息
          this.takeDeliveryFormModel = {
            receiptQuantity: data.receiptQuantity || "",
            receiptOperator: data.receiptOperator || "",
            receiptTime: data.receiptTime || "",
            receiptRemark: data.receiptRemark || "",
          };

          // 绑定操作表单
          this.operationFormModel = {
            receiptQuantity: data.receiptQuantity || "",
          };

          // 绑定药材列表到表格
          this.herbList = data.herbList || [];
          this.detailTableData = this.herbList;

          // 绑定图片
          this.deliveryImages = data.deliveryImages || [];

          // 更新标题中的计划编号
          this.$nextTick(() => {
            const titleElement = document.querySelector(".title-code");
            if (titleElement && data.planCode) {
              titleElement.textContent = data.planCode;
            }
          });
        }
      })
      .catch((error) => {
        console.error("获取供货计划详情失败:", error);
        this.$message.error("获取详情失败，请重试");
      });
  },
  methods: {
    back() {
      this.$router.replace({
        path: "supplyPlan",
        query: { activeName: "first" },
      });
    },

    customSummaryMethod(params) {
      const { columns, data } = params;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 3) {
          // 第四列显示汇总标题
          sums[index] = "发货总数量:";
        } else if (column.property === "actualQuantity") {
          const values = data.map((item) => Number(item[column.property]));
          const total = values.reduce((prev, curr) => {
            const value = Number(curr);
            return isNaN(value) ? prev : prev + curr;
          }, 0);
          sums[index] = `${total} ${this.basicFormModel.unitName || "kg"}`;
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },

    // 获取运输类型文本
    getTransportTypeText(type) {
      const typeMap = {
        1: "自有车辆",
        2: "第三方车辆",
      };
      return typeMap[type] || type || "";
    },

    save() {
      this.$refs["operationForm"].validate((valid) => {
        console.log(valid);
        if (!valid) {
          this.$message.error("请输入实收数量");
          return;
        } else {
          // TODO: 实现确认收货逻辑
          this.$message.success("收货确认成功");
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.button-container {
  line-height: 60px;
  text-align: center;
}
.title-code {
  color: #8a8989;
  font-size: 12px;
  margin-left: 20px;
}
.table-title {
  text-align: center;
  width: 100%;
  display: inline-block;
  line-height: 30px;
  margin-top: 10px;
  font-size: 18px;
  font-weight: 700;
}
/* 直接针对汇总行的样式 */
.el-table__footer tr td {
  font-weight: bold !important;
}
::v-deep .el-table thead {
  color: #000 !important;
}

::v-deep .el-table thead tr th {
  color: #000 !important;
}
</style>
